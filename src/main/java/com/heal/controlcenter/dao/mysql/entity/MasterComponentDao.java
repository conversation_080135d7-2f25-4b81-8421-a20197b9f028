package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.MasterComponentBean;
import com.heal.controlcenter.beans.MasterComponentTypeBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.pojo.Controller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * DAO for master component operations.
 */
@Slf4j
@Repository
public class MasterComponentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Finds component by name, version and account ID.
     * @param componentName The component name
     * @param componentVersion The component version
     * @param accountId The account ID
     * @return MasterComponentBean if found, null otherwise
     */
    public MasterComponentBean findByNameVersionAndAccountId(String componentName, String componentVersion, int accountId) {
        String sql = "SELECT mc.id, mc.name, mc.mst_component_type_id as componentTypeId, " +
                "mct.name as componentTypeName, mcv.id as componentVersionId, mcv.name as componentVersionName, " +
                "mcv.mst_common_version_id as commonVersionId, cv.name as commonVersionName, " +
                "mc.account_id as accountId, mc.description, mc.status, mc.created_time as createdTime, " +
                "mc.updated_time as updatedTime, mc.user_details_id as userDetailsId " +
                "FROM mst_component mc " +
                "JOIN mst_component_type mct ON mc.mst_component_type_id = mct.id " +
                "JOIN mst_component_version mcv ON mc.id = mcv.mst_component_id " +
                "JOIN mst_common_version cv ON mcv.mst_common_version_id = cv.id " +
                "WHERE mc.name = ? AND mcv.name = ? AND mc.account_id IN (1, ?)";

        try {
            List<MasterComponentBean> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(MasterComponentBean.class), componentName, componentVersion, accountId);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching component by name and version: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Finds component type by name and account ID.
     * @param name The component type name
     * @param accountId The account ID
     * @return MasterComponentTypeBean if found, null otherwise
     */
    public MasterComponentTypeBean findByNameAndAccountId(String name, int accountId) {
        String sql = "SELECT id, name, description, is_custom as isCustom, status, " +
                "created_time as createdTime, updated_time as updatedTime, " +
                "user_details_id as userDetailsId, account_id as accountId " +
                "FROM mst_component_type " +
                "WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        try {
            List<MasterComponentTypeBean> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(MasterComponentTypeBean.class), accountId, name);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching component type by name: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets component details with name and version.
     * Original JDBI query: "select mc.id,mc.name,mc.mst_component_type_id componentTypeId,mct.name componentTypeName,mcv.id componentVersionId,mcv.name componentVersionName,mcv.mst_common_version_id commonVersionId,cv.name commonVersionName from mst_component mc,mst_component_type mct,mst_component_version mcv,mst_common_version cv where mc.mst_component_type_id=mct.id and mc.id=mcv.mst_component_id and mcv.mst_common_version_id=cv.id and mc.name=:componentName and mcv.name=:componentVersion and mc.account_id in (1,:accountId)"
     * @param componentName The component name
     * @param componentVersion The component version
     * @param accountId The account ID
     * @return MasterComponentBean if found, null otherwise
     */
    public MasterComponentBean getComponentDetailsWithNameAndVersion(String componentName, String componentVersion, int accountId) {
        String sql = "SELECT mc.id, mc.name, mc.mst_component_type_id as componentTypeId, mct.name as componentTypeName, " +
                "mcv.id as componentVersionId, mcv.name as componentVersionName, mcv.mst_common_version_id as commonVersionId, " +
                "cv.name as commonVersionName " +
                "FROM mst_component mc, mst_component_type mct, mst_component_version mcv, mst_common_version cv " +
                "WHERE mc.mst_component_type_id = mct.id AND mc.id = mcv.mst_component_id AND mcv.mst_common_version_id = cv.id " +
                "AND mc.name = ? AND mcv.name = ? AND mc.account_id IN (1, ?)";

        try {
            List<MasterComponentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                MasterComponentBean bean = new MasterComponentBean();
                bean.setId(rs.getInt("id"));
                bean.setName(rs.getString("name"));
                bean.setComponentTypeId(rs.getInt("componentTypeId"));
                bean.setComponentTypeName(rs.getString("componentTypeName"));
                bean.setComponentVersionId(rs.getInt("componentVersionId"));
                bean.setComponentVersionName(rs.getString("componentVersionName"));
                bean.setCommonVersionId(rs.getInt("commonVersionId"));
                bean.setCommonVersionName(rs.getString("commonVersionName"));
                return bean;
            }, componentName, componentVersion, accountId);

            return results.stream()
                    .filter(c -> c.getName().equals(componentName) && c.getComponentVersionName().equals(componentVersion))
                    .findAny()
                    .orElse(null);
        } catch (Exception e) {
            log.error("Error getting component details: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets master component type using name.
     * @param componentTypeName The component type name
     * @param accountId The account ID
     * @return MasterComponentTypeBean if found, null otherwise
     */
    public MasterComponentTypeBean getMasterComponentTypeUsingName(String componentTypeName, String accountId) {
        String sql = "SELECT id, name, description, is_custom as isCustom, status, " +
                "created_time as createdTime, updated_time as updatedTime, " +
                "user_details_id as userDetailsId, account_id as accountId " +
                "FROM mst_component_type " +
                "WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        try {
            List<MasterComponentTypeBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                MasterComponentTypeBean bean = new MasterComponentTypeBean();
                bean.setId(rs.getInt("id"));
                bean.setName(rs.getString("name"));
                bean.setDescription(rs.getString("description"));
                bean.setIsCustom(rs.getInt("isCustom"));
                bean.setStatus(rs.getInt("status"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setAccountId(rs.getInt("accountId"));
                return bean;
            }, Integer.parseInt(accountId), componentTypeName);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting component type: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets all controllers for account.
     * @param accountId The account ID
     * @return List of Controller objects
     */
    public List<Controller> getControllerList(int accountId) {
        String sql = "SELECT id as appId, name, controller_type_id as controllerTypeId, identifier, " +
                "plugin_supr_interval as pluginSuppressionInterval, plugin_whitelist_status as pluginWhitelisted, " +
                "status, user_details_id as createdBY, created_time as createdOn, " +
                "updated_time as updatedTime, account_id as accountId " +
                "FROM controller WHERE account_id = ? AND status = 1";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                Controller controller = new Controller();
                controller.setAppId(rs.getString("appId"));
                controller.setName(rs.getString("name"));
                controller.setControllerTypeId(rs.getInt("controllerTypeId"));
                controller.setIdentifier(rs.getString("identifier"));
                controller.setPluginSuppressionInterval(rs.getInt("pluginSuppressionInterval"));
                controller.setPluginWhitelisted(rs.getBoolean("pluginWhitelisted"));
                controller.setStatus(rs.getInt("status"));
                controller.setCreatedBY(rs.getString("createdBY"));
                controller.setCreatedOn(rs.getString("createdOn"));
                controller.setUpdatedTime(rs.getString("updatedTime"));
                controller.setAccountId(rs.getInt("accountId"));
                return controller;
            }, accountId);
        } catch (Exception e) {
            log.error("Error getting controller list for account: {}", accountId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets master type for subtype name.
     * @param typeName The type name
     * @param subTypeName The subtype name
     * @param accountId The account ID
     * @return ViewTypes if found, null otherwise
     */
    public ViewTypesBean getMstTypeForSubTypeName(String typeName, String subTypeName, int accountId) {
        String sql = "SELECT mt.id as typeId, mt.name as typeName, mst.sub_type_id as subTypeId, mst.sub_type_name as subTypeName " +
                "FROM mst_type mt " +
                "JOIN mst_sub_type mst ON mt.id = mst.mst_type_id " +
                "WHERE LOWER(mt.name) = LOWER(?) AND LOWER(mst.sub_type_name) = LOWER(?) " +
                "AND mst.account_id IN (1, ?)";

        try {
            List<ViewTypesBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                ViewTypesBean viewType = new ViewTypesBean();
                viewType.setTypeId(rs.getInt("typeId"));
                viewType.setTypeName(rs.getString("typeName"));
                viewType.setSubTypeId(rs.getInt("subTypeId"));
                viewType.setSubTypeName(rs.getString("subTypeName"));
                return viewType;
            }, typeName.trim(), subTypeName.trim(), accountId);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type view from DB. Reason: {}", e.getMessage());
            return null;
        }
    }
}
